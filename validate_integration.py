#!/usr/bin/env python3
"""
Integration validation script for Phase 1.4 merge.

This script validates that all the merged components are properly integrated
and that the models, schemas, and API structure are consistent.
"""

import ast
import os
from pathlib import Path


def validate_model_imports():
    """Validate that all models are properly imported."""
    models_init = Path("src/cso_platform/models/__init__.py")
    
    if not models_init.exists():
        return False, "models/__init__.py not found"
    
    content = models_init.read_text()
    
    # Check for all expected imports
    expected_models = [
        "User", "UserRole", "ROICalculation", "Calculation", 
        "CalculationType", "CalculationStatus", "CalculationTemplate",
        "ReportTemplate", "Report", "ReportExport", "BrandConfiguration",
        "ReportType", "ExportFormat", "ReportStatus"
    ]
    
    missing = []
    for model in expected_models:
        if model not in content:
            missing.append(model)
    
    if missing:
        return False, f"Missing models in __init__.py: {missing}"
    
    return True, "All models properly imported"


def validate_user_model():
    """Validate that User model has all expected relationships."""
    user_model = Path("src/cso_platform/models/user.py")
    
    if not user_model.exists():
        return False, "user.py not found"
    
    content = user_model.read_text()
    
    # Check for all expected relationships
    expected_relationships = [
        "calculations", "roi_calculations", "created_templates",
        "generated_reports", "report_exports", "brand_configurations"
    ]
    
    missing = []
    for rel in expected_relationships:
        if f"{rel} = relationship" not in content:
            missing.append(rel)
    
    if missing:
        return False, f"Missing relationships in User model: {missing}"
    
    return True, "User model has all expected relationships"


def validate_api_structure():
    """Validate that API router includes all endpoints."""
    router_file = Path("src/cso_platform/api/v1/router.py")
    
    if not router_file.exists():
        return False, "router.py not found"
    
    content = router_file.read_text()
    
    # Check for all expected endpoint imports and inclusions
    expected_endpoints = ["auth", "users", "roi_calculations"]
    
    missing = []
    for endpoint in expected_endpoints:
        if f"from src.cso_platform.api.v1.endpoints import {endpoint}" not in content and \
           f", {endpoint}" not in content:
            missing.append(endpoint)
    
    if missing:
        return False, f"Missing endpoint imports: {missing}"
    
    return True, "All API endpoints properly included"


def validate_file_structure():
    """Validate that all expected files exist."""
    expected_files = [
        "src/cso_platform/models/user.py",
        "src/cso_platform/models/roi_calculation.py", 
        "src/cso_platform/models/calculation.py",
        "src/cso_platform/models/report.py",
        "src/cso_platform/schemas/user.py",
        "src/cso_platform/schemas/report.py",
        "src/cso_platform/api/v1/endpoints/auth.py",
        "src/cso_platform/api/v1/endpoints/users.py",
        "src/cso_platform/data/report_templates.py",
        "alembic/versions/001_add_report_tables.py"
    ]
    
    missing = []
    for file_path in expected_files:
        if not Path(file_path).exists():
            missing.append(file_path)
    
    if missing:
        return False, f"Missing files: {missing}"
    
    return True, "All expected files present"


def main():
    """Run all validation checks."""
    print("🔍 Validating Phase 1.4 Integration...")
    print("=" * 50)
    
    checks = [
        ("File Structure", validate_file_structure),
        ("Model Imports", validate_model_imports),
        ("User Model", validate_user_model),
        ("API Structure", validate_api_structure),
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            passed, message = check_func()
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{status} {check_name}: {message}")
            
            if not passed:
                all_passed = False
                
        except Exception as e:
            print(f"❌ FAIL {check_name}: Exception - {e}")
            all_passed = False
    
    print("=" * 50)
    
    if all_passed:
        print("🎉 ALL INTEGRATION CHECKS PASSED!")
        print("✅ Phase 1.1 + 1.2 + 1.4 successfully integrated")
        print("🚀 Ready for Phase 1.4 Step 2: FastAPI + TDD")
    else:
        print("⚠️  Some integration checks failed")
        print("🔧 Please review and fix the issues above")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
