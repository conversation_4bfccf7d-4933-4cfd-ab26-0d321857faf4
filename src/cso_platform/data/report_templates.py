"""Default report templates for Phase 1.4 Professional Reporting.

This module contains predefined report templates that provide users
with ready-to-use report configurations for common cybersecurity
reporting scenarios.
"""

from typing import Dict, List, Any

from src.cso_platform.models.report import ReportType, ExportFormat


def get_default_templates() -> List[Dict[str, Any]]:
    """Get list of default report templates to seed the database.
    
    Returns:
        List of template configurations for database seeding.
    """
    
    return [
        {
            "name": "Executive Risk Summary",
            "description": "High-level risk overview for executive leadership",
            "report_type": ReportType.EXECUTIVE_SUMMARY,
            "is_default": True,
            "is_system": True,
            "template_config": {
                "sections": [
                    {
                        "id": "executive_summary",
                        "title": "Executive Summary",
                        "type": "text",
                        "required": True,
                        "order": 1
                    },
                    {
                        "id": "risk_overview",
                        "title": "Risk Overview",
                        "type": "chart",
                        "chart_type": "risk_heatmap",
                        "required": True,
                        "order": 2
                    },
                    {
                        "id": "top_risks",
                        "title": "Top 10 Risks",
                        "type": "table",
                        "required": True,
                        "order": 3
                    },
                    {
                        "id": "investment_recommendations",
                        "title": "Investment Recommendations",
                        "type": "text",
                        "required": True,
                        "order": 4
                    }
                ],
                "layout": {
                    "page_size": "letter",
                    "orientation": "portrait",
                    "margins": {"top": 1, "bottom": 1, "left": 1, "right": 1}
                }
            },
            "style_config": {
                "colors": {
                    "primary": "#1f4e79",
                    "secondary": "#70ad47",
                    "accent": "#c5504b"
                },
                "fonts": {
                    "heading": "Arial Bold",
                    "body": "Arial",
                    "size": {"heading": 14, "body": 11}
                }
            },
            "supported_formats": [ExportFormat.PDF, ExportFormat.POWERPOINT]
        },
        {
            "name": "ROI Analysis Report",
            "description": "Detailed return on investment analysis for security investments",
            "report_type": ReportType.ROI_ANALYSIS,
            "is_default": True,
            "is_system": True,
            "template_config": {
                "sections": [
                    {
                        "id": "investment_overview",
                        "title": "Investment Overview",
                        "type": "text",
                        "required": True,
                        "order": 1
                    },
                    {
                        "id": "cost_breakdown",
                        "title": "Cost Breakdown",
                        "type": "table",
                        "required": True,
                        "order": 2
                    },
                    {
                        "id": "roi_calculations",
                        "title": "ROI Calculations",
                        "type": "chart",
                        "chart_type": "roi_waterfall",
                        "required": True,
                        "order": 3
                    },
                    {
                        "id": "risk_reduction",
                        "title": "Risk Reduction Analysis",
                        "type": "chart",
                        "chart_type": "before_after_comparison",
                        "required": True,
                        "order": 4
                    },
                    {
                        "id": "payback_analysis",
                        "title": "Payback Period Analysis",
                        "type": "chart",
                        "chart_type": "payback_timeline",
                        "required": True,
                        "order": 5
                    }
                ],
                "layout": {
                    "page_size": "letter",
                    "orientation": "portrait",
                    "margins": {"top": 1, "bottom": 1, "left": 1, "right": 1}
                }
            },
            "style_config": {
                "colors": {
                    "primary": "#2e75b6",
                    "secondary": "#70ad47",
                    "accent": "#ffc000"
                },
                "fonts": {
                    "heading": "Calibri Bold",
                    "body": "Calibri",
                    "size": {"heading": 14, "body": 11}
                }
            },
            "supported_formats": [ExportFormat.PDF, ExportFormat.EXCEL, ExportFormat.POWERPOINT]
        },
        {
            "name": "Quarterly Security Review",
            "description": "Comprehensive quarterly review of security posture and metrics",
            "report_type": ReportType.QUARTERLY_REVIEW,
            "is_default": True,
            "is_system": True,
            "template_config": {
                "sections": [
                    {
                        "id": "quarter_summary",
                        "title": "Quarter Summary",
                        "type": "text",
                        "required": True,
                        "order": 1
                    },
                    {
                        "id": "security_metrics",
                        "title": "Security Metrics Dashboard",
                        "type": "chart",
                        "chart_type": "metrics_dashboard",
                        "required": True,
                        "order": 2
                    },
                    {
                        "id": "incident_summary",
                        "title": "Security Incidents",
                        "type": "table",
                        "required": True,
                        "order": 3
                    },
                    {
                        "id": "compliance_status",
                        "title": "Compliance Status",
                        "type": "chart",
                        "chart_type": "compliance_scorecard",
                        "required": True,
                        "order": 4
                    },
                    {
                        "id": "budget_utilization",
                        "title": "Budget Utilization",
                        "type": "chart",
                        "chart_type": "budget_vs_actual",
                        "required": True,
                        "order": 5
                    },
                    {
                        "id": "next_quarter_priorities",
                        "title": "Next Quarter Priorities",
                        "type": "text",
                        "required": True,
                        "order": 6
                    }
                ],
                "layout": {
                    "page_size": "letter",
                    "orientation": "portrait",
                    "margins": {"top": 1, "bottom": 1, "left": 1, "right": 1}
                }
            },
            "style_config": {
                "colors": {
                    "primary": "#44546a",
                    "secondary": "#5b9bd5",
                    "accent": "#a5a5a5"
                },
                "fonts": {
                    "heading": "Segoe UI Bold",
                    "body": "Segoe UI",
                    "size": {"heading": 14, "body": 11}
                }
            },
            "supported_formats": [ExportFormat.PDF, ExportFormat.POWERPOINT, ExportFormat.EXCEL]
        },
        {
            "name": "Investment Justification",
            "description": "Business case template for security investment proposals",
            "report_type": ReportType.INVESTMENT_JUSTIFICATION,
            "is_default": True,
            "is_system": True,
            "template_config": {
                "sections": [
                    {
                        "id": "business_case",
                        "title": "Business Case",
                        "type": "text",
                        "required": True,
                        "order": 1
                    },
                    {
                        "id": "current_risk_state",
                        "title": "Current Risk State",
                        "type": "chart",
                        "chart_type": "risk_assessment",
                        "required": True,
                        "order": 2
                    },
                    {
                        "id": "proposed_solution",
                        "title": "Proposed Solution",
                        "type": "text",
                        "required": True,
                        "order": 3
                    },
                    {
                        "id": "cost_benefit_analysis",
                        "title": "Cost-Benefit Analysis",
                        "type": "table",
                        "required": True,
                        "order": 4
                    },
                    {
                        "id": "implementation_timeline",
                        "title": "Implementation Timeline",
                        "type": "chart",
                        "chart_type": "gantt",
                        "required": True,
                        "order": 5
                    },
                    {
                        "id": "risk_mitigation",
                        "title": "Risk Mitigation",
                        "type": "table",
                        "required": True,
                        "order": 6
                    }
                ],
                "layout": {
                    "page_size": "letter",
                    "orientation": "portrait",
                    "margins": {"top": 1, "bottom": 1, "left": 1, "right": 1}
                }
            },
            "style_config": {
                "colors": {
                    "primary": "#0f4c75",
                    "secondary": "#3282b8",
                    "accent": "#bbe1fa"
                },
                "fonts": {
                    "heading": "Times New Roman Bold",
                    "body": "Times New Roman",
                    "size": {"heading": 14, "body": 12}
                }
            },
            "supported_formats": [ExportFormat.PDF, ExportFormat.WORD, ExportFormat.POWERPOINT]
        }
    ]


def get_default_brand_configuration() -> Dict[str, Any]:
    """Get default brand configuration for organizations.
    
    Returns:
        Default brand configuration dictionary.
    """
    
    return {
        "name": "Default Corporate Branding",
        "is_default": True,
        "primary_color": "#1f4e79",
        "secondary_color": "#70ad47",
        "font_family": "Arial, sans-serif",
        "footer_text": "Confidential - Internal Use Only",
        "watermark_text": None,
        "custom_css": """
        .report-header {
            border-bottom: 2px solid #1f4e79;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .risk-high {
            background-color: #c5504b;
            color: white;
        }
        
        .risk-medium {
            background-color: #ffc000;
            color: black;
        }
        
        .risk-low {
            background-color: #70ad47;
            color: white;
        }
        """
    }
