"""Unit tests for Monte Carlo simulation engine.

This module contains comprehensive unit tests for the Monte Carlo simulation engine
used in enhanced risk calculations.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch

from src.cso_platform.utils.monte_carlo import (
    MonteCarloEngine, DistributionParameter, RiskCalculationFunction
)


class TestMonteCarloEngine:
    """Test cases for MonteCarloEngine."""
    
    @pytest.fixture
    def engine(self):
        """Monte Carlo engine instance with fixed seed."""
        return MonteCarloEngine(random_seed=42)
    
    @pytest.fixture
    def sample_parameters(self):
        """Sample distribution parameters for testing."""
        return [
            DistributionParameter(
                name="breach_cost",
                distribution_type="lognormal",
                parameters={"mean": 15.4, "std": 0.5},
                min_value=1000000.0,
                max_value=50000000.0
            ),
            DistributionParameter(
                name="breach_probability",
                distribution_type="beta",
                parameters={"alpha": 2, "beta": 5, "min": 0.05, "max": 0.95},
                min_value=0.05,
                max_value=0.95
            ),
            DistributionParameter(
                name="risk_reduction",
                distribution_type="triangular",
                parameters={"min": 0.05, "mode": 0.15, "max": 0.35},
                min_value=0.05,
                max_value=0.35
            )
        ]
    
    @pytest.fixture
    def simple_calc_function(self):
        """Simple calculation function for testing."""
        def calc_func(params):
            return params.get("breach_cost", 0) * params.get("breach_probability", 0) * params.get("risk_reduction", 0)
        return calc_func
    
    # Distribution Sample Generation Tests
    
    def test_generate_normal_samples(self, engine):
        """Test generation of normal distribution samples."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="normal",
            parameters={"mean": 100, "std": 15}
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert abs(np.mean(samples) - 100) < 5  # Should be close to mean
        assert abs(np.std(samples) - 15) < 3   # Should be close to std
    
    def test_generate_lognormal_samples(self, engine):
        """Test generation of lognormal distribution samples."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="lognormal",
            parameters={"mean": 2.0, "std": 0.5}
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(s > 0 for s in samples)  # Lognormal samples should be positive
        assert np.min(samples) > 0
    
    def test_generate_uniform_samples(self, engine):
        """Test generation of uniform distribution samples."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="uniform",
            parameters={"min": 10, "max": 20}
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(10 <= s <= 20 for s in samples)
        assert abs(np.mean(samples) - 15) < 1  # Should be close to midpoint
    
    def test_generate_triangular_samples(self, engine):
        """Test generation of triangular distribution samples."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="triangular",
            parameters={"min": 0, "mode": 5, "max": 10}
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(0 <= s <= 10 for s in samples)
    
    def test_generate_beta_samples(self, engine):
        """Test generation of beta distribution samples."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="beta",
            parameters={"alpha": 2, "beta": 5, "min": 0, "max": 1}
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(0 <= s <= 1 for s in samples)
    
    def test_generate_gamma_samples(self, engine):
        """Test generation of gamma distribution samples."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="gamma",
            parameters={"shape": 2, "scale": 3}
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(s >= 0 for s in samples)  # Gamma samples should be non-negative
    
    def test_generate_exponential_samples(self, engine):
        """Test generation of exponential distribution samples."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="exponential",
            parameters={"rate": 0.5}
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(s >= 0 for s in samples)  # Exponential samples should be non-negative
    
    def test_generate_samples_with_constraints(self, engine):
        """Test sample generation with min/max constraints."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="normal",
            parameters={"mean": 100, "std": 50},
            min_value=80,
            max_value=120
        )
        iterations = 1000
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(80 <= s <= 120 for s in samples)
    
    def test_generate_samples_unsupported_distribution(self, engine):
        """Test handling of unsupported distribution types."""
        # Arrange
        param = DistributionParameter(
            name="test_param",
            distribution_type="unsupported",
            parameters={"param1": 1},
            min_value=0,
            max_value=1
        )
        iterations = 100
        
        # Act
        samples = engine._generate_samples(param, iterations)
        
        # Assert
        assert len(samples) == iterations
        assert all(0 <= s <= 1 for s in samples)  # Should fallback to uniform
    
    # Monte Carlo Simulation Tests
    
    def test_run_simulation_success(self, engine, sample_parameters, simple_calc_function):
        """Test successful Monte Carlo simulation run."""
        # Arrange
        iterations = 1000
        
        # Act
        results = engine.run_simulation(sample_parameters, iterations, simple_calc_function)
        
        # Assert
        assert "results" in results
        assert "statistics" in results
        assert "sensitivity_analysis" in results
        assert "iterations" in results
        assert "successful_iterations" in results
        assert "convergence_achieved" in results
        
        assert results["iterations"] == iterations
        assert len(results["results"]) <= iterations  # Some may fail
        assert results["successful_iterations"] >= iterations * 0.9  # At least 90% success
    
    def test_run_simulation_with_failing_calculations(self, engine, sample_parameters):
        """Test simulation handling when some calculations fail."""
        # Arrange
        def failing_calc_function(params):
            if params.get("breach_cost", 0) > 10000000:  # Fail for high values
                raise ValueError("Calculation failed")
            return params.get("breach_cost", 0) * 0.001
        
        iterations = 100
        
        # Act
        results = engine.run_simulation(sample_parameters, iterations, failing_calc_function)
        
        # Assert
        assert results["successful_iterations"] < iterations
        assert len(results["results"]) == results["successful_iterations"]
        assert all(not np.isnan(r) for r in results["results"])
    
    # Statistics Calculation Tests
    
    def test_calculate_statistics(self, engine):
        """Test statistical calculations from simulation results."""
        # Arrange
        test_results = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        
        # Act
        stats = engine._calculate_statistics(test_results)
        
        # Assert
        assert "mean" in stats
        assert "median" in stats
        assert "std" in stats
        assert "variance" in stats
        assert "min" in stats
        assert "max" in stats
        assert "skewness" in stats
        assert "kurtosis" in stats
        assert "percentile_5" in stats
        assert "percentile_25" in stats
        assert "percentile_75" in stats
        assert "percentile_95" in stats
        assert "percentile_99" in stats
        
        assert stats["mean"] == 55.0
        assert stats["median"] == 55.0
        assert stats["min"] == 10.0
        assert stats["max"] == 100.0
    
    def test_calculate_statistics_empty_results(self, engine):
        """Test statistics calculation with empty results."""
        # Arrange
        test_results = []
        
        # Act
        stats = engine._calculate_statistics(test_results)
        
        # Assert
        assert stats == {}
    
    # Sensitivity Analysis Tests
    
    def test_sensitivity_analysis(self, engine, sample_parameters):
        """Test sensitivity analysis calculation."""
        # Arrange
        # Create mock samples and results with known correlations
        samples = {
            "breach_cost": np.array([1, 2, 3, 4, 5]),
            "breach_probability": np.array([0.1, 0.2, 0.3, 0.4, 0.5]),
            "risk_reduction": np.array([0.1, 0.15, 0.2, 0.25, 0.3])
        }
        results = [0.01, 0.06, 0.18, 0.4, 0.75]  # Strongly correlated with product
        
        # Act
        sensitivity = engine._sensitivity_analysis(samples, results, sample_parameters)
        
        # Assert
        assert "parameter_sensitivities" in sensitivity
        assert "correlation_matrix" in sensitivity
        assert "tornado_chart_data" in sensitivity
        assert "correlation_details" in sensitivity
        
        # Check that sensitivities are calculated
        assert len(sensitivity["parameter_sensitivities"]) == 3
        assert all(0 <= sens <= 1 for sens in sensitivity["parameter_sensitivities"].values())
    
    def test_sensitivity_analysis_insufficient_data(self, engine, sample_parameters):
        """Test sensitivity analysis with insufficient data."""
        # Arrange
        samples = {"param1": np.array([1, 2])}
        results = [1, 2]
        
        # Act
        sensitivity = engine._sensitivity_analysis(samples, results, sample_parameters[:1])
        
        # Assert
        assert sensitivity["parameter_sensitivities"] == {}
        assert sensitivity["correlation_matrix"] == {}
    
    # Convergence Tests
    
    def test_check_convergence_converged(self, engine):
        """Test convergence detection when simulation has converged."""
        # Arrange
        # Create results that converge to a stable mean
        results = [100] * 1000 + [101] * 1000  # Very stable
        
        # Act
        converged = engine._check_convergence(results, window_size=500)
        
        # Assert
        assert converged is True
    
    def test_check_convergence_not_converged(self, engine):
        """Test convergence detection when simulation hasn't converged."""
        # Arrange
        # Create results with significant change between windows
        results = [50] * 1000 + [100] * 1000  # 100% change
        
        # Act
        converged = engine._check_convergence(results, window_size=500)
        
        # Assert
        assert converged is False
    
    def test_check_convergence_insufficient_data(self, engine):
        """Test convergence check with insufficient data."""
        # Arrange
        results = [1, 2, 3]  # Too few results
        
        # Act
        converged = engine._check_convergence(results, window_size=100)
        
        # Assert
        assert converged is False
    
    # VaR Calculation Tests
    
    def test_calculate_var(self, engine):
        """Test Value at Risk calculation."""
        # Arrange
        results = list(range(1, 101))  # 1 to 100
        
        # Act
        var_results = engine.calculate_var(results, confidence_levels=[0.95, 0.99])
        
        # Assert
        assert "var_95" in var_results
        assert "var_99" in var_results
        assert "conditional_var_95" in var_results
        
        # VaR at 95% should be 5th percentile
        assert var_results["var_95"] == 5.0
        # VaR at 99% should be 1st percentile
        assert var_results["var_99"] == 1.0
        # Conditional VaR should be mean of tail
        assert var_results["conditional_var_95"] <= var_results["var_95"]
    
    def test_calculate_var_empty_results(self, engine):
        """Test VaR calculation with empty results."""
        # Arrange
        results = []
        
        # Act
        var_results = engine.calculate_var(results)
        
        # Assert
        assert var_results == {}
    
    # Confidence Intervals Tests
    
    def test_calculate_confidence_intervals(self, engine):
        """Test confidence interval calculation."""
        # Arrange
        results = list(range(1, 101))  # 1 to 100
        
        # Act
        intervals = engine.calculate_confidence_intervals(results, confidence_levels=[5, 25, 50, 75, 95])
        
        # Assert
        assert "percentile_5" in intervals
        assert "percentile_25" in intervals
        assert "percentile_50" in intervals
        assert "percentile_75" in intervals
        assert "percentile_95" in intervals
        
        assert intervals["percentile_5"] == 5.0
        assert intervals["percentile_25"] == 25.0
        assert intervals["percentile_50"] == 50.5  # Median
        assert intervals["percentile_75"] == 75.0
        assert intervals["percentile_95"] == 95.0
    
    def test_calculate_confidence_intervals_empty_results(self, engine):
        """Test confidence interval calculation with empty results."""
        # Arrange
        results = []
        
        # Act
        intervals = engine.calculate_confidence_intervals(results)
        
        # Assert
        assert intervals == {}


class TestRiskCalculationFunction:
    """Test cases for RiskCalculationFunction."""
    
    @pytest.fixture
    def base_data(self):
        """Sample base calculation data."""
        return {
            "internal_cost": 200000,
            "direct_savings": 100000
        }
    
    @pytest.fixture
    def risk_data(self):
        """Sample risk profile data."""
        return {
            "industry": "healthcare",
            "organization_size": "medium",
            "breach_cost_multiplier": 5.5,
            "breach_probability": 0.27
        }
    
    @pytest.fixture
    def calc_function(self, base_data, risk_data):
        """Risk calculation function instance."""
        return RiskCalculationFunction(base_data, risk_data)
    
    def test_risk_calculation_function_success(self, calc_function):
        """Test successful risk calculation."""
        # Arrange
        parameters = {
            "breach_cost": 5000000,
            "breach_probability": 0.25,
            "risk_reduction_factor": 0.15
        }
        
        # Act
        result = calc_function(parameters)
        
        # Assert
        assert isinstance(result, float)
        assert result > 0  # Should be positive ROI
        
        # Verify calculation logic
        expected_annual_loss = 5000000 * 0.25  # 1,250,000
        expected_risk_reduction = expected_annual_loss * 0.15  # 187,500
        expected_total_benefits = 100000 + 187500  # 287,500
        expected_roi = (287500 / 200000) * 100  # 143.75%
        
        assert abs(result - expected_roi) < 0.01
    
    def test_risk_calculation_function_with_defaults(self, calc_function):
        """Test risk calculation with default parameter values."""
        # Arrange
        parameters = {}  # Empty parameters, should use defaults
        
        # Act
        result = calc_function(parameters)
        
        # Assert
        assert isinstance(result, float)
        assert result > 0
    
    def test_risk_calculation_function_error_handling(self, calc_function):
        """Test error handling in risk calculation function."""
        # Arrange
        parameters = {
            "breach_cost": "invalid",  # Invalid type
            "breach_probability": 0.25,
            "risk_reduction_factor": 0.15
        }
        
        # Act
        result = calc_function(parameters)
        
        # Assert
        assert np.isnan(result)  # Should return NaN on error
