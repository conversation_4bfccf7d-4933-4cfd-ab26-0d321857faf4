"""Simple integration tests for authentication.

This module tests the key authentication functionality for Phase 1.2.
"""

import pytest
import os
from fastapi.testclient import Test<PERSON>lient
from fastapi import status

# Set test environment
os.environ["ENVIRONMENT"] = "testing"

from src.cso_platform.main import app


@pytest.fixture(autouse=True)
def clean_database():
    """Clean database before each test."""
    # Remove test database file if it exists
    import os
    test_db_path = "./cso_platform_test.db"
    if os.path.exists(test_db_path):
        os.remove(test_db_path)
    yield
    # Clean up after test
    if os.path.exists(test_db_path):
        os.remove(test_db_path)


def test_complete_auth_flow():
    """Test complete authentication flow: register -> login -> access protected endpoint."""
    with TestClient(app) as client:
        # 1. Register a new user
        user_data = {
            "email": "<EMAIL>",
            "username": "testflowuser",
            "password": "SecurePassword123",
            "full_name": "Test Flow User",
            "organization_name": "Test Flow Corp",
            "industry": "Technology",
            "employee_count": 100
        }
        
        register_response = client.post("/api/v1/auth/register", json=user_data)
        assert register_response.status_code == status.HTTP_201_CREATED
        
        user_profile = register_response.json()
        assert user_profile["email"] == user_data["email"]
        assert user_profile["username"] == user_data["username"]
        assert user_profile["organization_name"] == user_data["organization_name"]
        
        # 2. Login with the registered user
        login_data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        
        login_response = client.post(
            "/api/v1/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert login_response.status_code == status.HTTP_200_OK
        token_data = login_response.json()
        assert "access_token" in token_data
        assert token_data["token_type"] == "bearer"
        
        token = token_data["access_token"]
        
        # 3. Access protected profile endpoint
        profile_response = client.get(
            "/api/v1/auth/profile",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert profile_response.status_code == status.HTTP_200_OK
        profile_data = profile_response.json()
        assert profile_data["email"] == user_data["email"]
        assert profile_data["username"] == user_data["username"]
        
        # 4. Create a ROI calculation (protected endpoint)
        calculation_data = {
            "calculation_name": "Test Flow Calculation",
            "external_annual_spend": 50000,
            "test_frequency": 4,
            "internal_fte_salary": 120000,
            "hours_per_test": 40,
            "risk_reduction_factor": 0.15,
            "notes": "Test calculation for auth flow"
        }
        
        calc_response = client.post(
            "/api/v1/roi-calculations/",
            json=calculation_data,
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert calc_response.status_code == status.HTTP_201_CREATED
        calc_result = calc_response.json()
        assert calc_result["calculation_name"] == calculation_data["calculation_name"]
        assert calc_result["user_id"] == user_profile["id"]
        
        # 5. List user's calculations
        list_response = client.get(
            "/api/v1/roi-calculations/",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert list_response.status_code == status.HTTP_200_OK
        calculations = list_response.json()
        assert len(calculations) == 1
        assert calculations[0]["id"] == calc_result["id"]
        
        # 6. Get user stats
        stats_response = client.get(
            "/api/v1/auth/stats",
            headers={"Authorization": f"Bearer {token}"}
        )
        
        assert stats_response.status_code == status.HTTP_200_OK
        stats_data = stats_response.json()
        assert stats_data["total_calculations"] == 1
        assert stats_data["username"] == user_data["username"]


def test_duplicate_registration():
    """Test that duplicate email/username registration fails."""
    with TestClient(app) as client:
        user_data = {
            "email": "<EMAIL>",
            "username": "duplicateuser",
            "password": "SecurePassword123",
            "full_name": "Duplicate User"
        }
        
        # First registration should succeed
        response1 = client.post("/api/v1/auth/register", json=user_data)
        assert response1.status_code == status.HTTP_201_CREATED
        
        # Second registration with same email should fail
        response2 = client.post("/api/v1/auth/register", json=user_data)
        assert response2.status_code == status.HTTP_400_BAD_REQUEST
        assert "Email already registered" in response2.json()["detail"]


def test_invalid_login():
    """Test login with invalid credentials."""
    with TestClient(app) as client:
        # Register user first
        user_data = {
            "email": "<EMAIL>",
            "username": "invalidloginuser",
            "password": "SecurePassword123",
            "full_name": "Invalid Login User"
        }
        
        client.post("/api/v1/auth/register", json=user_data)
        
        # Try login with wrong password
        login_response = client.post(
            "/api/v1/auth/login",
            data={
                "username": user_data["username"],
                "password": "WrongPassword"
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert login_response.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Incorrect username or password" in login_response.json()["detail"]


def test_protected_endpoint_without_auth():
    """Test accessing protected endpoints without authentication."""
    with TestClient(app) as client:
        # Try to access profile without token
        response = client.get("/api/v1/auth/profile")
        assert response.status_code == status.HTTP_403_FORBIDDEN
        
        # Try to create calculation without token
        calc_data = {
            "calculation_name": "Unauthorized Test",
            "external_annual_spend": 50000,
            "test_frequency": 4,
            "internal_fte_salary": 120000,
            "hours_per_test": 40,
            "risk_reduction_factor": 0.15
        }
        
        response = client.post("/api/v1/roi-calculations/", json=calc_data)
        assert response.status_code == status.HTTP_403_FORBIDDEN


def test_quick_calculation_anonymous():
    """Test that quick calculations work for anonymous users."""
    with TestClient(app) as client:
        calc_data = {
            "external_annual_spend": 40000,
            "test_frequency": 3,
            "internal_fte_salary": 110000,
            "hours_per_test": 30,
            "risk_reduction_factor": 0.12
        }
        
        response = client.post("/api/v1/roi-calculations/quick-calculate", json=calc_data)
        assert response.status_code == status.HTTP_200_OK
        
        result = response.json()
        assert "annual_savings" in result
        assert "roi_percentage" in result
        assert "payback_months" in result


def test_login_with_email():
    """Test login using email instead of username."""
    with TestClient(app) as client:
        user_data = {
            "email": "<EMAIL>",
            "username": "emailloginuser",
            "password": "SecurePassword123",
            "full_name": "Email Login User"
        }
        
        # Register user
        client.post("/api/v1/auth/register", json=user_data)
        
        # Login with email
        login_response = client.post(
            "/api/v1/auth/login",
            data={
                "username": user_data["email"],  # Using email as username
                "password": user_data["password"]
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert login_response.status_code == status.HTTP_200_OK
        token_data = login_response.json()
        assert "access_token" in token_data


def test_user_isolation():
    """Test that users can only see their own calculations."""
    with TestClient(app) as client:
        # Register two users
        user1_data = {
            "email": "<EMAIL>",
            "username": "user1",
            "password": "SecurePassword123",
            "full_name": "User One"
        }
        
        user2_data = {
            "email": "<EMAIL>",
            "username": "user2",
            "password": "SecurePassword123",
            "full_name": "User Two"
        }
        
        client.post("/api/v1/auth/register", json=user1_data)
        client.post("/api/v1/auth/register", json=user2_data)
        
        # Login as user1
        login1_response = client.post(
            "/api/v1/auth/login",
            data={"username": "user1", "password": "SecurePassword123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        token1 = login1_response.json()["access_token"]
        
        # Login as user2
        login2_response = client.post(
            "/api/v1/auth/login",
            data={"username": "user2", "password": "SecurePassword123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        token2 = login2_response.json()["access_token"]
        
        # User1 creates a calculation
        calc_data = {
            "calculation_name": "User1 Calculation",
            "external_annual_spend": 50000,
            "test_frequency": 4,
            "internal_fte_salary": 120000,
            "hours_per_test": 40,
            "risk_reduction_factor": 0.15
        }
        
        client.post(
            "/api/v1/roi-calculations/",
            json=calc_data,
            headers={"Authorization": f"Bearer {token1}"}
        )
        
        # User1 should see their calculation
        user1_calcs = client.get(
            "/api/v1/roi-calculations/",
            headers={"Authorization": f"Bearer {token1}"}
        )
        assert len(user1_calcs.json()) == 1
        
        # User2 should see no calculations
        user2_calcs = client.get(
            "/api/v1/roi-calculations/",
            headers={"Authorization": f"Bearer {token2}"}
        )
        assert len(user2_calcs.json()) == 0
