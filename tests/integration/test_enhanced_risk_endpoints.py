"""Integration tests for enhanced risk calculation endpoints.

This module contains integration tests for Phase 1.3 Enhanced Risk & Cost Modeling
API endpoints, testing the full request/response cycle.
"""

import pytest
from decimal import Decimal
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.cso_platform.main import app
from src.cso_platform.models.calculation import (
    RiskProfile, EnhancedRiskCalculation, Calculation, IndustryType, OrganizationSize, RiskLevel
)
from src.cso_platform.models.user import User
from tests.conftest import TestDatabase


class TestEnhancedRiskEndpoints:
    """Integration tests for enhanced risk endpoints."""
    
    @pytest.fixture
    def client(self):
        """Test client for API requests."""
        return TestClient(app)
    
    @pytest.fixture
    def test_user(self, test_db: Session):
        """Create a test user."""
        user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed_password",
            is_active=True
        )
        test_db.add(user)
        test_db.commit()
        test_db.refresh(user)
        return user
    
    @pytest.fixture
    def auth_headers(self, test_user):
        """Authentication headers for API requests."""
        # Mock JWT token for testing
        return {"Authorization": "Bearer test_token"}
    
    @pytest.fixture
    def test_calculation(self, test_db: Session, test_user):
        """Create a test base calculation."""
        calculation = Calculation(
            name="Test Base Calculation",
            description="Base calculation for testing",
            calculation_type="roi_basic",
            total_cost=Decimal("200000"),
            total_benefit=Decimal("100000"),
            user_id=test_user.id
        )
        test_db.add(calculation)
        test_db.commit()
        test_db.refresh(calculation)
        return calculation
    
    @pytest.fixture
    def sample_risk_profile_data(self):
        """Sample risk profile data for API requests."""
        return {
            "name": "Healthcare Organization Profile",
            "description": "Risk profile for a medium-sized healthcare organization",
            "industry": "healthcare",
            "organization_size": "medium",
            "annual_revenue": "50000000",
            "employee_count": 1200,
            "data_sensitivity_level": 5,
            "regulatory_requirements": ["HIPAA", "SOC2"],
            "previous_incidents": 2,
            "current_security_maturity": 3,
            "geographic_regions": ["North America"],
            "business_criticality": "high",
            "is_template": False,
            "is_public": False
        }
    
    # Risk Profile Endpoint Tests
    
    @pytest.mark.asyncio
    async def test_create_risk_profile_success(self, client, auth_headers, sample_risk_profile_data):
        """Test successful risk profile creation."""
        # Act
        response = client.post(
            "/api/v1/enhanced-risk/risk-profiles",
            json=sample_risk_profile_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 201
        data = response.json()
        
        assert data["name"] == sample_risk_profile_data["name"]
        assert data["industry"] == sample_risk_profile_data["industry"]
        assert data["organization_size"] == sample_risk_profile_data["organization_size"]
        assert data["employee_count"] == sample_risk_profile_data["employee_count"]
        assert data["data_sensitivity_level"] == sample_risk_profile_data["data_sensitivity_level"]
        assert data["regulatory_requirements"] == sample_risk_profile_data["regulatory_requirements"]
        assert "breach_cost_multiplier" in data
        assert "breach_probability" in data
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data
    
    @pytest.mark.asyncio
    async def test_create_risk_profile_validation_error(self, client, auth_headers):
        """Test risk profile creation with validation errors."""
        # Arrange
        invalid_data = {
            "name": "",  # Empty name should fail validation
            "industry": "healthcare",
            "organization_size": "medium",
            "data_sensitivity_level": 6,  # Invalid level (should be 1-5)
            "previous_incidents": -1,  # Negative incidents should fail
            "current_security_maturity": 0  # Invalid maturity (should be 1-5)
        }
        
        # Act
        response = client.post(
            "/api/v1/enhanced-risk/risk-profiles",
            json=invalid_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 422  # Validation error
        error_data = response.json()
        assert "detail" in error_data
    
    @pytest.mark.asyncio
    async def test_list_risk_profiles(self, client, auth_headers, test_db, test_user):
        """Test listing risk profiles."""
        # Arrange - Create test risk profiles
        profile1 = RiskProfile(
            name="Profile 1",
            industry=IndustryType.HEALTHCARE,
            organization_size=OrganizationSize.MEDIUM,
            data_sensitivity_level=3,
            previous_incidents=0,
            current_security_maturity=3,
            business_criticality=RiskLevel.MEDIUM,
            user_id=test_user.id
        )
        profile2 = RiskProfile(
            name="Profile 2",
            industry=IndustryType.FINANCIAL,
            organization_size=OrganizationSize.LARGE,
            data_sensitivity_level=4,
            previous_incidents=1,
            current_security_maturity=4,
            business_criticality=RiskLevel.HIGH,
            user_id=test_user.id
        )
        test_db.add_all([profile1, profile2])
        test_db.commit()
        
        # Act
        response = client.get(
            "/api/v1/enhanced-risk/risk-profiles",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) >= 2
        
        # Check that profiles are returned with calculated metrics
        for profile in data:
            assert "id" in profile
            assert "name" in profile
            assert "industry" in profile
            assert "organization_size" in profile
            assert "breach_cost_multiplier" in profile
            assert "breach_probability" in profile
    
    @pytest.mark.asyncio
    async def test_get_risk_profile_success(self, client, auth_headers, test_db, test_user):
        """Test getting a specific risk profile."""
        # Arrange
        profile = RiskProfile(
            name="Test Profile",
            industry=IndustryType.TECHNOLOGY,
            organization_size=OrganizationSize.SMALL,
            data_sensitivity_level=3,
            previous_incidents=0,
            current_security_maturity=3,
            business_criticality=RiskLevel.MEDIUM,
            user_id=test_user.id
        )
        test_db.add(profile)
        test_db.commit()
        test_db.refresh(profile)
        
        # Act
        response = client.get(
            f"/api/v1/enhanced-risk/risk-profiles/{profile.id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == profile.id
        assert data["name"] == profile.name
        assert data["industry"] == profile.industry.value
        assert data["organization_size"] == profile.organization_size.value
        assert "breach_cost_multiplier" in data
        assert "breach_probability" in data
    
    @pytest.mark.asyncio
    async def test_get_risk_profile_not_found(self, client, auth_headers):
        """Test getting a non-existent risk profile."""
        # Act
        response = client.get(
            "/api/v1/enhanced-risk/risk-profiles/999",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 404
        error_data = response.json()
        assert "not found" in error_data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_update_risk_profile_success(self, client, auth_headers, test_db, test_user):
        """Test updating a risk profile."""
        # Arrange
        profile = RiskProfile(
            name="Original Profile",
            industry=IndustryType.RETAIL,
            organization_size=OrganizationSize.MEDIUM,
            data_sensitivity_level=3,
            previous_incidents=0,
            current_security_maturity=3,
            business_criticality=RiskLevel.MEDIUM,
            user_id=test_user.id
        )
        test_db.add(profile)
        test_db.commit()
        test_db.refresh(profile)
        
        update_data = {
            "name": "Updated Profile",
            "data_sensitivity_level": 4,
            "current_security_maturity": 4
        }
        
        # Act
        response = client.put(
            f"/api/v1/enhanced-risk/risk-profiles/{profile.id}",
            json=update_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data["name"] == "Updated Profile"
        assert data["data_sensitivity_level"] == 4
        assert data["current_security_maturity"] == 4
        assert data["industry"] == "retail"  # Unchanged
    
    @pytest.mark.asyncio
    async def test_delete_risk_profile_success(self, client, auth_headers, test_db, test_user):
        """Test deleting a risk profile."""
        # Arrange
        profile = RiskProfile(
            name="Profile to Delete",
            industry=IndustryType.GOVERNMENT,
            organization_size=OrganizationSize.LARGE,
            data_sensitivity_level=5,
            previous_incidents=0,
            current_security_maturity=4,
            business_criticality=RiskLevel.HIGH,
            user_id=test_user.id
        )
        test_db.add(profile)
        test_db.commit()
        test_db.refresh(profile)
        
        # Act
        response = client.delete(
            f"/api/v1/enhanced-risk/risk-profiles/{profile.id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 204
        
        # Verify profile is soft deleted
        test_db.refresh(profile)
        assert profile.deleted_at is not None
    
    # Enhanced Risk Calculation Endpoint Tests
    
    @pytest.mark.asyncio
    async def test_create_enhanced_calculation_success(
        self, client, auth_headers, test_db, test_user, test_calculation
    ):
        """Test successful enhanced risk calculation creation."""
        # Arrange
        risk_profile = RiskProfile(
            name="Test Risk Profile",
            industry=IndustryType.HEALTHCARE,
            organization_size=OrganizationSize.MEDIUM,
            data_sensitivity_level=4,
            previous_incidents=1,
            current_security_maturity=3,
            business_criticality=RiskLevel.HIGH,
            user_id=test_user.id
        )
        test_db.add(risk_profile)
        test_db.commit()
        test_db.refresh(risk_profile)
        
        calc_data = {
            "name": "Healthcare Security Investment Analysis",
            "description": "Monte Carlo analysis for security tool investment",
            "base_calculation_id": test_calculation.id,
            "risk_profile_id": risk_profile.id,
            "simulation_iterations": 1000,  # Smaller for testing
            "random_seed": 42,
            "monte_carlo_parameters": [
                {
                    "parameter_name": "breach_cost",
                    "parameter_description": "Cost of a data breach",
                    "distribution_type": "lognormal",
                    "distribution_parameters": {"mean": 15.4, "std": 0.5},
                    "min_value": "1000000",
                    "max_value": "50000000",
                    "source": "IBM Cost of Breach 2024",
                    "confidence_level": "0.85"
                }
            ]
        }
        
        # Act
        response = client.post(
            "/api/v1/enhanced-risk/calculations",
            json=calc_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 201
        data = response.json()
        
        assert data["name"] == calc_data["name"]
        assert data["base_calculation_id"] == calc_data["base_calculation_id"]
        assert data["risk_profile_id"] == calc_data["risk_profile_id"]
        assert data["simulation_iterations"] == calc_data["simulation_iterations"]
        assert data["random_seed"] == calc_data["random_seed"]
        
        # Check that results are calculated
        assert "enhanced_roi_percentage" in data
        assert "risk_adjusted_value" in data
        assert "expected_annual_loss" in data
        
        # Check VaR results
        assert "var_results" in data
        if data["var_results"]:
            assert "var_95_percent" in data["var_results"]
            assert "var_99_percent" in data["var_results"]
        
        # Check confidence intervals
        assert "roi_confidence_intervals" in data
        
        # Check Monte Carlo parameters
        assert "monte_carlo_parameters" in data
        assert len(data["monte_carlo_parameters"]) == 1
        
        mc_param = data["monte_carlo_parameters"][0]
        assert mc_param["parameter_name"] == "breach_cost"
        assert mc_param["distribution_type"] == "lognormal"
    
    @pytest.mark.asyncio
    async def test_create_enhanced_calculation_base_not_found(self, client, auth_headers, test_db, test_user):
        """Test enhanced calculation creation with non-existent base calculation."""
        # Arrange
        risk_profile = RiskProfile(
            name="Test Risk Profile",
            industry=IndustryType.TECHNOLOGY,
            organization_size=OrganizationSize.SMALL,
            data_sensitivity_level=3,
            previous_incidents=0,
            current_security_maturity=3,
            business_criticality=RiskLevel.MEDIUM,
            user_id=test_user.id
        )
        test_db.add(risk_profile)
        test_db.commit()
        test_db.refresh(risk_profile)
        
        calc_data = {
            "name": "Test Calculation",
            "base_calculation_id": 999,  # Non-existent
            "risk_profile_id": risk_profile.id,
            "simulation_iterations": 1000,
            "monte_carlo_parameters": []
        }
        
        # Act
        response = client.post(
            "/api/v1/enhanced-risk/calculations",
            json=calc_data,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 404
        error_data = response.json()
        assert "not found" in error_data["detail"].lower()
    
    @pytest.mark.asyncio
    async def test_list_enhanced_calculations(self, client, auth_headers, test_db, test_user, test_calculation):
        """Test listing enhanced calculations."""
        # Arrange
        risk_profile = RiskProfile(
            name="Test Risk Profile",
            industry=IndustryType.FINANCIAL,
            organization_size=OrganizationSize.LARGE,
            data_sensitivity_level=4,
            previous_incidents=0,
            current_security_maturity=4,
            business_criticality=RiskLevel.HIGH,
            user_id=test_user.id
        )
        test_db.add(risk_profile)
        test_db.commit()
        test_db.refresh(risk_profile)
        
        enhanced_calc = EnhancedRiskCalculation(
            name="Test Enhanced Calculation",
            base_calculation_id=test_calculation.id,
            risk_profile_id=risk_profile.id,
            user_id=test_user.id,
            simulation_iterations=1000,
            enhanced_roi_percentage=Decimal("125.50"),
            risk_adjusted_value=Decimal("500000.00"),
            expected_annual_loss=Decimal("75000.00")
        )
        test_db.add(enhanced_calc)
        test_db.commit()
        
        # Act
        response = client.get(
            "/api/v1/enhanced-risk/calculations",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert isinstance(data, list)
        assert len(data) >= 1
        
        calc = data[0]
        assert calc["name"] == "Test Enhanced Calculation"
        assert calc["simulation_iterations"] == 1000
        assert "enhanced_roi_percentage" in calc
    
    @pytest.mark.asyncio
    async def test_get_enhanced_calculation_success(self, client, auth_headers, test_db, test_user, test_calculation):
        """Test getting a specific enhanced calculation."""
        # Arrange
        risk_profile = RiskProfile(
            name="Test Risk Profile",
            industry=IndustryType.ENERGY,
            organization_size=OrganizationSize.ENTERPRISE,
            data_sensitivity_level=5,
            previous_incidents=2,
            current_security_maturity=3,
            business_criticality=RiskLevel.CRITICAL,
            user_id=test_user.id
        )
        test_db.add(risk_profile)
        test_db.commit()
        test_db.refresh(risk_profile)
        
        enhanced_calc = EnhancedRiskCalculation(
            name="Detailed Enhanced Calculation",
            description="Comprehensive risk analysis",
            base_calculation_id=test_calculation.id,
            risk_profile_id=risk_profile.id,
            user_id=test_user.id,
            simulation_iterations=5000,
            enhanced_roi_percentage=Decimal("145.75"),
            risk_adjusted_value=Decimal("750000.00"),
            expected_annual_loss=Decimal("125000.00"),
            var_95_percent=Decimal("300000.00"),
            var_99_percent=Decimal("500000.00"),
            conditional_var_95=Decimal("350000.00")
        )
        test_db.add(enhanced_calc)
        test_db.commit()
        test_db.refresh(enhanced_calc)
        
        # Act
        response = client.get(
            f"/api/v1/enhanced-risk/calculations/{enhanced_calc.id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data["id"] == enhanced_calc.id
        assert data["name"] == "Detailed Enhanced Calculation"
        assert data["simulation_iterations"] == 5000
        assert float(data["enhanced_roi_percentage"]) == 145.75
        assert float(data["risk_adjusted_value"]) == 750000.00
        
        # Check VaR results
        assert "var_results" in data
        var_results = data["var_results"]
        assert float(var_results["var_95_percent"]) == 300000.00
        assert float(var_results["var_99_percent"]) == 500000.00
        assert float(var_results["conditional_var_95"]) == 350000.00
    
    @pytest.mark.asyncio
    async def test_get_calculation_summary(self, client, auth_headers, test_db, test_user, test_calculation):
        """Test getting enhanced calculation summary."""
        # Arrange
        risk_profile = RiskProfile(
            name="Summary Test Profile",
            industry=IndustryType.MANUFACTURING,
            organization_size=OrganizationSize.MEDIUM,
            data_sensitivity_level=3,
            previous_incidents=1,
            current_security_maturity=3,
            business_criticality=RiskLevel.MEDIUM,
            user_id=test_user.id
        )
        test_db.add(risk_profile)
        test_db.commit()
        test_db.refresh(risk_profile)
        
        enhanced_calc = EnhancedRiskCalculation(
            name="Summary Test Calculation",
            base_calculation_id=test_calculation.id,
            risk_profile_id=risk_profile.id,
            user_id=test_user.id,
            simulation_iterations=2000,
            enhanced_roi_percentage=Decimal("135.25"),
            calculation_duration_ms=1500
        )
        test_db.add(enhanced_calc)
        test_db.commit()
        test_db.refresh(enhanced_calc)
        
        # Act
        response = client.get(
            f"/api/v1/enhanced-risk/calculations/{enhanced_calc.id}/summary",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 200
        data = response.json()
        
        assert data["iterations"] == 2000
        assert data["enhanced_roi"] == 135.25
        assert data["calculation_time_ms"] == 1500
    
    @pytest.mark.asyncio
    async def test_delete_enhanced_calculation(self, client, auth_headers, test_db, test_user, test_calculation):
        """Test deleting an enhanced calculation."""
        # Arrange
        risk_profile = RiskProfile(
            name="Delete Test Profile",
            industry=IndustryType.EDUCATION,
            organization_size=OrganizationSize.MEDIUM,
            data_sensitivity_level=2,
            previous_incidents=0,
            current_security_maturity=2,
            business_criticality=RiskLevel.LOW,
            user_id=test_user.id
        )
        test_db.add(risk_profile)
        test_db.commit()
        test_db.refresh(risk_profile)
        
        enhanced_calc = EnhancedRiskCalculation(
            name="Calculation to Delete",
            base_calculation_id=test_calculation.id,
            risk_profile_id=risk_profile.id,
            user_id=test_user.id,
            simulation_iterations=1000
        )
        test_db.add(enhanced_calc)
        test_db.commit()
        test_db.refresh(enhanced_calc)
        
        # Act
        response = client.delete(
            f"/api/v1/enhanced-risk/calculations/{enhanced_calc.id}",
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 204
        
        # Verify calculation is soft deleted
        test_db.refresh(enhanced_calc)
        assert enhanced_calc.deleted_at is not None
